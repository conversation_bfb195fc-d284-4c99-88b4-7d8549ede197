{"name": "spreadsheets-plugin", "version": "1.0.1", "description": "Plugin to create spreadsheets in Obsidian.", "main": "main.js", "scripts": {"dev": "node esbuild.config.mjs", "build": "tsc -noEmit -skipLibCheck && node esbuild.config.mjs production", "version": "node version-bump.mjs && git add manifest.json versions.json"}, "keywords": [], "author": "", "license": "MIT", "devDependencies": {"@types/node": "^16.11.6", "@types/react": "^18.2.18", "@types/react-dom": "^18.2.7", "@typescript-eslint/eslint-plugin": "5.29.0", "@typescript-eslint/parser": "5.29.0", "builtin-modules": "3.3.0", "esbuild": "0.17.3", "obsidian": "latest", "tslib": "2.4.0", "typescript": "4.7.4"}, "dependencies": {"@fortune-sheet/react": "^0.19.0", "react": "^18.2.0", "react-dom": "^18.2.0"}}