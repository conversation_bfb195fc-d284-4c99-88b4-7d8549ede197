import { TextFileView } from "obsidian";

export const VIEW_TYPE_SPREADSHEET = "spreadsheet-view";



import { ItemView, WorkspaceLeaf } from "obsidian";
import * as React from "react";
import * as ReactDOM from "react-dom";
import { createRoot } from "react-dom/client";

import { Workbook } from "@fortune-sheet/react";
import "@fortune-sheet/react/dist/index.css"

function set_ctx_pos(el) {
  let rect=el.getBoundingClientRect();
  let p =  {x:rect.left,y:rect.top};

  let r = document.querySelector(':root');
  if(p.x){
    r.style.setProperty('--ctx_menu_x',  -1*p.x + "px" );
    r.style.setProperty('--ctx_menu_y', (-1*p.y + 50 )+ "px"  );
  }
  
  // console.log("pos is set to ")
  // console.log( -1*p.x + "px")
  // console.log(   (-1*p.y + 50 )+ "px"  )

}


function transformData(responseData) {
  return responseData.map((sheet) => ({
    id: sheet.id,
    name: sheet.name,
    plugin: "divams_spreadsheets_for_obsidian",
    config: sheet.config,
    celldata: (sheet.data || []).flatMap((row, rIndex) =>
      row
        .map((cell, cIndex) => {
          if (cell !== null) {
            return {
              r: rIndex,
              c: cIndex,
              v: cell,
            };
          }

          return undefined;
        })
        .filter((cell) => cell !== undefined),
    ),
    calcChain: (sheet.calcChain || []).map((item) => {
      const relatedCell = sheet.data[item.r][item.c];
      return {
        r: item.r,
        c: item.c,
        id: item.id,
        v: relatedCell !== null ? relatedCell : null,
      };
    }),
  }));
}

function handleData(receivedData) {
  const newData = transformData(receivedData);

  if (receivedData.length > 0 && receivedData[0].calcChain) {
    newData[0].calcChain = receivedData[0].calcChain;
  }

  return newData;

}



export class SpreadsheetView extends TextFileView {
  table_element: HTMLElement;
  spreadsheet_container : HTMLElement;
  sheet_data_in : any;
  sheet_data_out : any;
  root : any;
  is_save_timer_wait : any;
  resize_observer : any;
  lastRightClickEvent: MouseEvent | null = null;

  getViewData() {
    if(this.sheet_data_out){
      let r =  JSON.stringify( handleData(this.sheet_data_out) , null,  4 ) ;
      console.log("saved!!")
      return r;
    } else {
      return ""
    }
  }

  // If clear is set, then it means we're opening a completely different file.
  setViewData(data: string, clear: boolean) {

    if(data.trim()){
      this.sheet_data_in = JSON.parse(data)
    } else {
      this.sheet_data_in = [{ name: "Sheet1" }];
    }

    this.refresh();
  }

  refresh() {
    this.table_element.empty();

    let spreadsheet_container = this.table_element.createEl("div");
    spreadsheet_container.setAttribute("style","background-color:black; width:calc(100% - 15px) ; height: calc( 100vh - 130px); color:black ; ")
    // add filter: invert(1); for dark mode #TODO: make a button in settings page

    this.resize_observer = new ResizeObserver(function(){
        window.dispatchEvent(new Event('resize'));
        set_ctx_pos(spreadsheet_container)
    }).observe(spreadsheet_container)

    //
    this.spreadsheet_container = spreadsheet_container;
    let that = this;

    const settings = {
      data: this.sheet_data_in, // sheet data
      onChange: (data:any) => { that.sheet_data_out = data;  that.maybe_save_data()}, // onChange event
      cellContextMenu: [
        "copy", // 复制
        "paste", // 粘贴
        "|",
        "insert-row", // 插入行
        "insert-column", // 插入列
        "delete-row", // 删除选中行
        "delete-column", // 删除选中列
        "delete-cell", // 删除单元格
        "hide-row", // 隐藏选中行和显示选中行
        "hide-column", // 隐藏选中列和显示选中列
        "clear", // 清除内容
        "sort", // 排序选区
        "filter", // 筛选选区
        "chart", // 图表生成
        "image", // 插入图片
        "link", // 插入链接
        "data", // 数据验证
        "cell-format" // 设置单元格格式
      ], // 使用默认菜单项
    }

    this.root = createRoot(spreadsheet_container);
    this.root.render(
        <Workbook {...settings} />
    );

    // 设置批注菜单增强
    this.setupCommentMenuEnhancement();

  }

  // 设置批注菜单增强
  setupCommentMenuEnhancement() {
    const that = this;

    // 监听右键菜单显示事件
    document.addEventListener('contextmenu', function(e) {
      // 保存右键点击的位置，用于确定单元格
      that.lastRightClickEvent = e;

      // 延迟检查，确保FortuneSheet的菜单已经生成
      setTimeout(() => {
        that.enhanceContextMenu(e);
      }, 100);
    });
  }

  // 增强右键菜单
  enhanceContextMenu(event: MouseEvent) {
    // 查找FortuneSheet的右键菜单
    const contextMenu = document.querySelector('.fortune-context-menu');
    if (!contextMenu) {
      return;
    }

    // 检查当前选中的单元格是否有批注
    const hasComment = this.checkCurrentCellHasComment();

    // 查找"插入图片"菜单项
    const imageMenuItem = this.findMenuItemByText(contextMenu, '插入图片') ||
                         this.findMenuItemByText(contextMenu, 'Insert Image') ||
                         this.findMenuItemByText(contextMenu, 'image');

    if (imageMenuItem) {
      // 在插入图片前添加批注菜单项
      if (hasComment) {
        this.addCommentMenuItem(contextMenu, imageMenuItem, '修改批注', 'edit-comment');
        this.addCommentMenuItem(contextMenu, imageMenuItem, '删除批注', 'delete-comment');
      } else {
        this.addCommentMenuItem(contextMenu, imageMenuItem, '新建批注', 'add-comment');
      }

      // 添加分隔符
      this.addMenuSeparator(contextMenu, imageMenuItem);
    }
  }

  // 查找菜单项
  findMenuItemByText(contextMenu: Element, text: string): Element | null {
    const menuItems = contextMenu.querySelectorAll('.luckysheet-cols-menuitem');
    for (let i = 0; i < menuItems.length; i++) {
      const item = menuItems[i];
      if (item.textContent?.includes(text)) {
        return item;
      }
    }
    return null;
  }

  // 添加批注菜单项
  addCommentMenuItem(contextMenu: Element, beforeElement: Element, text: string, action: string) {
    // 检查是否已经添加过，避免重复
    if (contextMenu.querySelector(`[data-comment-action="${action}"]`)) {
      return;
    }

    const menuItem = document.createElement('div');
    menuItem.className = 'luckysheet-cols-menuitem';
    menuItem.setAttribute('data-comment-action', action);
    menuItem.innerHTML = `
      <div class="luckysheet-cols-menuitem-content">
        📝 ${text}
      </div>
    `;

    // 添加点击事件
    menuItem.addEventListener('click', (e) => {
      e.preventDefault();
      e.stopPropagation();
      this.handleCommentAction(action);
      // 关闭菜单
      contextMenu.remove();
    });

    // 添加悬停效果
    menuItem.addEventListener('mouseenter', () => {
      menuItem.classList.add('luckysheet-cols-menuitem-hover');
    });

    menuItem.addEventListener('mouseleave', () => {
      menuItem.classList.remove('luckysheet-cols-menuitem-hover');
    });

    // 插入到指定位置前
    contextMenu.insertBefore(menuItem, beforeElement);
  }

  // 添加菜单分隔符
  addMenuSeparator(contextMenu: Element, beforeElement: Element) {
    // 检查是否已经有分隔符
    const prevElement = beforeElement.previousElementSibling;
    if (prevElement && prevElement.classList.contains('fortune-context-menu-divider')) {
      return;
    }

    const separator = document.createElement('div');
    separator.className = 'fortune-context-menu-divider';
    contextMenu.insertBefore(separator, beforeElement);
  }

  // 检查当前单元格是否有批注
  checkCurrentCellHasComment(): boolean {
    try {
      // 获取当前选中的单元格信息
      const selectedCell = this.getCurrentSelectedCell();
      if (!selectedCell) {
        return false;
      }

      const { row, col } = selectedCell;

      // 检查当前表格数据中是否有批注
      if (this.sheet_data_out && this.sheet_data_out.length > 0) {
        const currentSheet = this.sheet_data_out[0]; // 假设使用第一个sheet
        if (currentSheet && currentSheet.data && currentSheet.data[row] && currentSheet.data[row][col]) {
          const cell = currentSheet.data[row][col];
          return cell && cell.ps && cell.ps.value;
        }
      }

      return false;
    } catch (error) {
      console.log("检查批注时出错:", error);
      return false;
    }
  }

  // 获取当前选中的单元格
  getCurrentSelectedCell(): { row: number, col: number } | null {
    try {
      // 如果有右键点击事件，尝试从点击位置确定单元格
      if (this.lastRightClickEvent) {
        const cellFromClick = this.getCellFromClickPosition(this.lastRightClickEvent);
        if (cellFromClick) {
          return cellFromClick;
        }
      }

      // 尝试从DOM中获取选中的单元格信息
      const selectedCell = document.querySelector('.luckysheet-cell-selected');
      if (selectedCell) {
        // 从样式中解析位置信息
        const style = selectedCell.getAttribute('style') || '';
        const leftMatch = style.match(/left:\s*(\d+)px/);
        const topMatch = style.match(/top:\s*(\d+)px/);

        if (leftMatch && topMatch) {
          const left = parseInt(leftMatch[1]);
          const top = parseInt(topMatch[1]);

          // 简单的位置到行列的转换（这个需要根据实际的单元格大小调整）
          const col = Math.floor(left / 73); // 假设列宽73px
          const row = Math.floor(top / 19);  // 假设行高19px

          return { row, col };
        }
      }

      // 如果都无法获取，返回默认值
      console.log("无法确定选中的单元格，使用默认值 [0, 0]");
      return { row: 0, col: 0 };
    } catch (error) {
      console.log("获取选中单元格时出错:", error);
      return { row: 0, col: 0 };
    }
  }

  // 从点击位置获取单元格
  getCellFromClickPosition(event: MouseEvent): { row: number, col: number } | null {
    try {
      const target = event.target as HTMLElement;

      // 查找最近的单元格元素
      let cellElement = target.closest('td[data-r][data-c]') as HTMLElement;
      if (!cellElement) {
        // 如果没有找到带data-r和data-c的元素，尝试其他方法
        cellElement = target.closest('.luckysheet-cell') as HTMLElement;
      }

      if (cellElement) {
        // 尝试从data属性获取行列信息
        const row = cellElement.getAttribute('data-r') || cellElement.getAttribute('data-row');
        const col = cellElement.getAttribute('data-c') || cellElement.getAttribute('data-col');

        if (row !== null && col !== null) {
          return { row: parseInt(row), col: parseInt(col) };
        }
      }

      // 如果无法从元素属性获取，尝试计算位置
      const rect = this.spreadsheet_container.getBoundingClientRect();
      const x = event.clientX - rect.left;
      const y = event.clientY - rect.top;

      // 简单的位置计算（需要根据实际情况调整）
      const col = Math.floor(x / 73); // 假设列宽73px
      const row = Math.floor(y / 19);  // 假设行高19px

      return { row: Math.max(0, row), col: Math.max(0, col) };
    } catch (error) {
      console.log("从点击位置获取单元格时出错:", error);
      return null;
    }
  }

  // 处理批注操作
  handleCommentAction(action: string) {
    console.log("批注操作:", action);

    switch (action) {
      case 'add-comment':
        this.addComment();
        break;
      case 'edit-comment':
        this.editComment();
        break;
      case 'delete-comment':
        this.deleteComment();
        break;
    }
  }

  // 添加批注
  addComment() {
    console.log("添加批注");

    const selectedCell = this.getCurrentSelectedCell();
    if (!selectedCell) {
      console.log("没有选中的单元格");
      return;
    }

    const { row, col } = selectedCell;

    try {
      // 确保数据结构存在
      if (!this.sheet_data_out || this.sheet_data_out.length === 0) {
        console.log("没有表格数据");
        return;
      }

      const currentSheet = this.sheet_data_out[0];
      if (!currentSheet.data) {
        currentSheet.data = [];
      }

      // 确保行存在
      if (!currentSheet.data[row]) {
        currentSheet.data[row] = [];
      }

      // 确保单元格存在
      if (!currentSheet.data[row][col]) {
        currentSheet.data[row][col] = {};
      }

      // 添加批注数据
      const cell = currentSheet.data[row][col];
      cell.ps = {
        left: col * 73 + 100, // 简单的位置计算
        top: row * 19 + 100,
        width: 200,
        height: 100,
        value: "请输入批注内容...",
        isShow: true // 显示批注框
      };

      // 触发数据保存
      this.maybe_save_data();

      console.log(`批注已添加到单元格 [${row}, ${col}]`);

      // 刷新视图以显示批注
      setTimeout(() => {
        this.refresh();
      }, 100);

    } catch (error) {
      console.error("添加批注时出错:", error);
    }
  }

  // 编辑批注
  editComment() {
    console.log("编辑批注");

    const selectedCell = this.getCurrentSelectedCell();
    if (!selectedCell) {
      return;
    }

    const { row, col } = selectedCell;

    try {
      if (this.sheet_data_out && this.sheet_data_out.length > 0) {
        const currentSheet = this.sheet_data_out[0];
        if (currentSheet.data && currentSheet.data[row] && currentSheet.data[row][col]) {
          const cell = currentSheet.data[row][col];
          if (cell.ps) {
            // 显示批注编辑框
            cell.ps.isShow = true;

            // 触发数据保存
            this.maybe_save_data();

            console.log(`批注编辑模式已开启 [${row}, ${col}]`);

            // 刷新视图
            setTimeout(() => {
              this.refresh();
            }, 100);
          }
        }
      }
    } catch (error) {
      console.error("编辑批注时出错:", error);
    }
  }

  // 删除批注
  deleteComment() {
    console.log("删除批注");

    const selectedCell = this.getCurrentSelectedCell();
    if (!selectedCell) {
      return;
    }

    const { row, col } = selectedCell;

    try {
      if (this.sheet_data_out && this.sheet_data_out.length > 0) {
        const currentSheet = this.sheet_data_out[0];
        if (currentSheet.data && currentSheet.data[row] && currentSheet.data[row][col]) {
          const cell = currentSheet.data[row][col];
          if (cell.ps) {
            // 删除批注数据
            delete cell.ps;

            // 触发数据保存
            this.maybe_save_data();

            console.log(`批注已删除 [${row}, ${col}]`);

            // 刷新视图
            setTimeout(() => {
              this.refresh();
            }, 100);
          }
        }
      }
    } catch (error) {
      console.error("删除批注时出错:", error);
    }
  }

  maybe_save_data(){
    let that = this;

    if(that.is_save_timer_wait)
      return;

    that.is_save_timer_wait = true;
     setTimeout(function(){
        
         that.requestSave();
         that.is_save_timer_wait = false;
     } , 4000 )
  }

  clear() {
  }

  getViewType() {
    return VIEW_TYPE_SPREADSHEET;
  }

  async onOpen() {
    this.table_element = this.contentEl.createEl("div");
  }

  async onClose() {

    if(this.resize_observer){
      this.resize_observer.disconnect()
    }
  
    this.requestSave();
    
    if(this.root)
      this.root.unmount()

    this.contentEl.empty();
  }
}